import { useIsFreeProduct, useRouteFields } from '@/hooks';
import { useABImpressionTracking } from '@/hooks/analytics';
import { CommerceProductBase } from '@/models';
import { getProductType } from '@/utils';
import { useProductTracking } from '@pmi/www-shared/analytics';
import { UserStatuses, useUserStatus } from '@pmi/www-shared/hooks';
import { useGetActiveMembershipsQuery } from '@pmi/www-shared/store';
import { useEffect } from 'react';

const Component: React.FC = () => {
  const product = useRouteFields<CommerceProductBase>();
  const { isFreeProductActivationEligible } = useIsFreeProduct(product);
  const userStatus = useUserStatus();
  const isAuthenticated = userStatus === UserStatuses.LoggedIn;
  const { isLoading: activeMembershipLoading } = useGetActiveMembershipsQuery(null, { skip: !isAuthenticated });
  const productTracking = useProductTracking();
  const { trackABImpression } = useABImpressionTracking();

  const sku = product?.ExternalID?.value as string;
  const productType = getProductType(product);

  useEffect(() => {
    productTracking();
    // Track A/B impression when product data is available
    if (product) {
      trackABImpression(product);
    }
  }, [product, productTracking, trackABImpression]);

  useEffect(() => {
    if (!activeMembershipLoading) {
      productTracking(productType, sku, isFreeProductActivationEligible);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeMembershipLoading]);

  return null;
};

export default Component;
