import { ABPriceData, CommerceProductBase } from '@/models';

export const parseABPrices = (abPricesField?: { value?: string }): ABPriceData[] => {
  if (!abPricesField?.value) {
    return [];
  }

  try {
    return JSON.parse(abPricesField.value) as ABPriceData[];
  } catch (error) {
    return [];
  }
};

export const getProductABTestInfo = (product: CommerceProductBase): { isEligible: boolean; variant: string | null } => {
  const abPricesData = parseABPrices(product.ABPrices);

  if (abPricesData.length === 0) {
    return { isEligible: false, variant: null };
  }

  // Find the first entry with a group_type (should be "A" or "B")
  const abTestEntry = abPricesData.find((entry) => entry.group_type);

  if (!abTestEntry) {
    return { isEligible: false, variant: null };
  }

  return {
    isEligible: true,
    variant: abTestEntry.group_type,
  };
};

export const generateTestName = (team: string, sku: string, version: string): string => {
  return `${team}:${sku}:${version}`;
};

export const getProductVariant = (product: CommerceProductBase): string | null => {
  const { isEligible, variant } = getProductABTestInfo(product);
  return isEligible ? variant : null;
};

export const generateVariantName = (testName: string, variant: string): string => {
  return `${testName}:${variant}`;
};

export const isProductEligibleForTest = (product: CommerceProductBase): boolean => {
  const { isEligible } = getProductABTestInfo(product);
  return isEligible;
};
