import { CommerceProductBase } from '@/models';
import { generateTestName, generateVariantName, getProductVariant, isProductEligibleForTest } from '@/utils/abTesting';
import { ABTestEventNames, emitEvent, EventTypes } from '@pmi/analytics-layer';
import { UserStatuses, useUserStatus } from '@pmi/www-shared/hooks';
import { useGetActiveMembershipsQuery } from '@pmi/www-shared/store';
import useSpxHeadlessSettings from '../Sitecore/useSpxHeadlessSettings';

interface ABImpressionEventData {
  ab: {
    testName: string;
    experienceName: string;
    status: 'member' | 'nonmember' | 'unknown';
  };
}

export const useABImpressionTracking = () => {
  const settings = useSpxHeadlessSettings();
  const userStatus = useUserStatus();
  const isAuthenticated = userStatus === UserStatuses.LoggedIn;
  const { data: activeMemberships } = useGetActiveMembershipsQuery(null, { skip: !isAuthenticated });

  const getMembershipStatus = (): 'member' | 'nonmember' | 'unknown' => {
    if (!isAuthenticated) {
      return 'unknown';
    }

    if (activeMemberships?.hasActiveMembership) {
      return 'member';
    }

    return 'nonmember';
  };

  const trackABImpression = (product: CommerceProductBase) => {
    try {
      const abTestConfig = settings?.ABTestConfig;
      console.log('settings', settings);
      if (!abTestConfig) {
        return;
      }

      const productSku = product?.ExternalID?.value as string;
      if (!productSku) {
        return;
      }

      if (!isProductEligibleForTest(product)) {
        return;
      }

      const variant = getProductVariant(product);
      if (!variant) {
        return;
      }

      const testName = generateTestName(abTestConfig.team, productSku, abTestConfig.version);

      const experienceName = generateVariantName(testName, variant);

      const membershipStatus = getMembershipStatus();

      const eventData: ABImpressionEventData = {
        ab: {
          testName,
          experienceName,
          status: membershipStatus,
        },
      };

      // Emit the A/B impression event
      emitEvent({
        eventType: EventTypes.ABTest,
        eventName: ABTestEventNames.ABImpression,
        eventData,
      });

      console.log('A/B impression tracked:', {
        testName,
        experienceName,
        productSku,
        variant,
      });
    } catch (error) {
      console.error('Failed to track A/B impression:', error);
    }
  };

  const trackABClick = (product: CommerceProductBase, clickTarget: string) => {
    try {
      const abTestConfig = settings?.ABTestConfig;
      if (!abTestConfig) {
        return;
      }

      const productSku = product?.ExternalID?.value as string;
      if (!productSku || !isProductEligibleForTest(product)) {
        return;
      }

      const variant = getProductVariant(product);
      if (!variant) {
        return;
      }

      // Build the test name dynamically from Sitecore settings
      const testName = generateTestName(abTestConfig.team, productSku, abTestConfig.version);
      const experienceName = generateVariantName(testName, variant);

      // Get membership status for the click event
      const membershipStatus = getMembershipStatus();

      emitEvent({
        eventType: EventTypes.ABTest,
        eventName: ABTestEventNames.ABClick,
        eventData: {
          ab: {
            testName,
            experienceName,
            abCTA: clickTarget,
            status: membershipStatus,
          },
        },
      });
    } catch (error) {
      console.error('Failed to track A/B click:', error);
    }
  };

  return {
    trackABImpression,
    trackABClick,
  };
};
